import service.Typesetter;

public class TestRunner {
    public static void main(String[] args) {
        Typesetter typesetter = new Typesetter();
        
        System.out.println("=== Test 1: Basic case ===");
        System.out.println(typesetter.wrap("a lot of words for a single line", 9));
        
        System.out.println("=== Test 2: Two short words ===");
        System.out.println(typesetter.wrap("hello world", 7));
        
        System.out.println("=== Test 3: One short word ===");
        System.out.println(typesetter.wrap("test", 4));
        
        System.out.println("=== Test 4: Ignores whitespace ===");
        System.out.println(typesetter.wrap("this is a test", 4));
        
        System.out.println("=== Test 5: Splits long words ===");
        System.out.println(typesetter.wrap("a longword", 7));
        
        System.out.println("=== Test 6: Splits long words in multiple lines ===");
        System.out.println(typesetter.wrap("reallylongword", 7));
    }
}
