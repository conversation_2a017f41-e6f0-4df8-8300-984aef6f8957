package service;

import java.util.ArrayList;

public class Typesetter {

    public String wrap(String text, int columns) {
        if (columns < 1)
            throw new IllegalArgumentException("Columns must be positive. Given: " + columns);
        if (text.isBlank())
            throw new IllegalArgumentException("Text must not be empty. Given: " + text);

        text = text.strip();
        ArrayList<String> lines = new ArrayList<>();
        String[] words = text.split(" ");

        StringBuilder currentLine = new StringBuilder();

        for (String word : words) {
            // Handle words that are longer than the column width
            while (word.length() > columns) {
                if (currentLine.length() > 0) {
                    // Finish current line first
                    lines.add(currentLine.toString());
                    currentLine = new StringBuilder();
                }

                // Split the long word
                int splitPoint = columns - 1; // Reserve space for hyphen
                String part = word.substring(0, splitPoint) + "-";
                lines.add(part);
                word = word.substring(splitPoint);
            }

            // Check if word fits on current line
            int spaceNeeded = word.length();
            if (currentLine.length() > 0) {
                spaceNeeded += 1; // for the space before the word
            }

            if (currentLine.length() + spaceNeeded <= columns) {
                // Word fits on current line
                if (currentLine.length() > 0) {
                    currentLine.append(" ");
                }
                currentLine.append(word);
            } else {
                // Word doesn't fit, start new line
                if (currentLine.length() > 0) {
                    lines.add(currentLine.toString());
                }
                currentLine = new StringBuilder(word);
            }
        }

        // Add the last line if it has content
        if (currentLine.length() > 0) {
            lines.add(currentLine.toString());
        }

        StringBuilder result = new StringBuilder();
        for (String line : lines) {
            result.append(line).append("\n");
        }
        return result.toString();
    }

    public static void main(String[] args) {
        var typesetter = new Typesetter();

        // Test the original case
        System.out.println("=== Test 1: Basic case ===");
        System.out.println(typesetter.wrap("a lot of words for a single line", 9));

        // Test word splitting
        System.out.println("=== Test 2: Word splitting ===");
        System.out.println(typesetter.wrap("a longword", 7));

        // Test multiple word splits
        System.out.println("=== Test 3: Multiple word splits ===");
        System.out.println(typesetter.wrap("reallylongword", 7));

        // Test the complex Lorem ipsum case
        System.out.println("=== Test 4: Lorem ipsum (first few lines) ===");
        String lorem = "Lorem ipsum dolor sit amet, consectetur adipiscing elit.";
        System.out.println(typesetter.wrap(lorem, 8));
    }

}
