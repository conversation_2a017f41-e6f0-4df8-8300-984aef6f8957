package service;

import java.util.ArrayList;

public class Typesetter {

    public String wrap(String text, int columns) {
        if (columns < 1)
            throw new IllegalArgumentException("Columns must be positive. Given: " + columns);
        if (text.isBlank())
            throw new IllegalArgumentException("Text must not be empty. Given: " + text);

        text = text.strip();
        ArrayList<String> lines = new ArrayList<>();
        String[] words = text.split(" ");

        StringBuilder currentLine = new StringBuilder();

        for (String word : words) {
            // Handle words that are longer than the column width

            while (word.length() > columns) {
                if (currentLine.length() > 0) {
                    // Split the long word
                    int splitPoint = currentLine.length() - columns - 1; // Reserve space for hyphen
                    String part = word.substring(0, splitPoint) + "-";
                    lines.add(part);
                    word = word.substring(splitPoint);
                    // Finish current line first
                    lines.add(currentLine.toString());
                    currentLine = new StringBuilder();
                }

                // Split the long word
                int splitPoint = currentLine.length() - columns - 1; // Reserve space for hyphen
                String part = word.substring(0, splitPoint) + "-";
                lines.add(part);
                word = word.substring(splitPoint);
            }

            // Check if word fits on current line
            int spaceNeeded = word.length();
            if (currentLine.length() > 0) {
                spaceNeeded += 1; // for the space before the word
            }

            if (currentLine.length() + spaceNeeded <= columns) {
                // Word fits on current line
                if (currentLine.length() > 0) {
                    currentLine.append(" ");
                }
                currentLine.append(word);
            } else {

                if (word.length() > columns) {
                    // Split the long word
                    int splitPoint = currentLine.length() - columns - 1; // Reserve space for hyphen
                    String part = word.substring(0, splitPoint) + "-";
                    lines.add(part);
                    word = word.substring(splitPoint);
                }
                // Word doesn't fit, start new line
                if (currentLine.length() > 0) {
                    lines.add(currentLine.toString());
                }
                currentLine = new StringBuilder(word);
            }
        }

        // Add the last line if it has content
        if (currentLine.length() > 0) {
            lines.add(currentLine.toString());
        }

        StringBuilder result = new StringBuilder();
        for (String line : lines) {
            result.append(line).append("\n");
        }
        return result.toString();
    }

    public static void main(String[] args) {
        var typesetter = new Typesetter();
        System.out.println(typesetter.wrap("Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus lacus, bibendum vitae tincidunt quis, vehicula non ex. Mauris vestibulum erat est, eu pulvinar sapien rhoncus non. Fusce ac elit eleifend, elementum neque non, consectetur dui. Phasellus sodales mollis finibus. Curabitur ultricies arcu eget quam tincidunt, pulvinar tristique ipsum convallis. Donec dictum interdum elit, eu gravida justo ornare vel. Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas. Donec orci magna, condimentum sed fringilla in, pellentesque ac nisl. Mauris interdum mi ac nulla pretium, ut aliquet nunc facilisis. Donec dui arcu, consectetur et purus eget, faucibus ornare nunc. Phasellus ac quam ipsum. Vestibulum sed justo aliquam, luctus eros ac, placerat lorem. Class aptent taciti sociosqu ad litora torquent per conubia nostra, per inceptos himenaeos. Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas. In nec vehicula erat, a tempor lorem. In posuere, magna vitae feugiat ultricies, arcu turpis vehicula eros, at ornare arcu arcu at purus.", 8));
    }

}
