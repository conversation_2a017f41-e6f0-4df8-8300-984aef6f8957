package service;

import java.util.ArrayList;

public class Typesetter {

    public String wrap(String text, int columns) {
        if (columns < 1)
            throw new IllegalArgumentException("Columns must be positive. Given: " + columns);
        if (text.isBlank())
            throw new IllegalArgumentException("Text must not be empty. Given: " + text);

        text = text.strip();
        ArrayList<String> columnsList = new ArrayList<>();
        String[] words = text.split(" ");
        int amount = words.length;

        int currentLineLength = 0;
        int start = 0;
        StringBuilder s = new StringBuilder();
        char[] charArray = text.toCharArray();
        int i = 0;


        while (i < amount) {
            if (words[i].length() > columns) {
                String shorterWord = words[i].substring(0, columns - currentLineLength - 1) + "-";
                String restOfWord = words[i].substring(columns - currentLineLength - 1);
                words[i] = restOfWord;
                s.append(shorterWord);
                columnsList.add(s.toString());
                s = new StringBuilder();
                currentLineLength = 0;

                continue;
            }
            if (currentLineLength + words[i].length() < columns) {
                s.append(words[i]).append(" ");
                currentLineLength += words[i].length() + 1;
                i++;
                if (columnsList.isEmpty()) {
                    start = i;
                    columnsList.add(s.toString());
                }
                else {
                    String currentInList = columnsList.get(columnsList.size() - 1);
                    columnsList.set(columnsList.size() - 1, currentInList.strip() + " " + words[i-1]);
                }
                s = new StringBuilder();

            }
            else if (currentLineLength + words[i].length() == columns) {
                s.append(words[i]);
                columnsList.add(s.toString());
                s = new StringBuilder();
                currentLineLength = 0;
                i++;
            }
            else {
                String shorterWord = words[i].substring(0, columns - currentLineLength - 1) + "-";
                s.append(shorterWord);
                columnsList.add(s.toString());
                s = new StringBuilder();
                currentLineLength = 0;
                i++;
            }
        }


        StringBuilder sb = new StringBuilder();
        for (String column : columnsList) {
            sb.append(column).append("\n");
        }
        return sb.toString();
    }

    public static void main(String[] args) {
        var typesetter = new Typesetter();
        System.out.println(typesetter.wrap("a lot of words for a single line", 9));
    }

}
