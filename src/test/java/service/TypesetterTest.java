package service;

import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

class TypesetterTest {

    @Nested
    class WrapFails {

        @ParameterizedTest
        @NullAndEmptySource
        void without_text(String text) {
            var typesetter = new Typesetter();

            assertThatThrownBy(() -> typesetter.wrap(text, 1))
                    .isInstanceOfAny(NullPointerException.class, IllegalArgumentException.class);
        }

        @ParameterizedTest
        @ValueSource(ints = {-1, 0})
        void for_nonpositive_columns(int columns) {
            var typesetter = new Typesetter();

            assertThatThrownBy(() -> typesetter.wrap("test", columns))
                    .isInstanceOf(IllegalArgumentException.class);
        }
    }

    @Nested
    class Wrapping {

        @ParameterizedTest
        @ValueSource(ints = {4, 5})
        void one_short_word(int columns) {
            var typesetter = new Typesetter();

            var result = typesetter.wrap("test", columns);

            assertThat(result)
                    .isEqualTo("""
                            test
                            """);
        }

        @Test
        void two_short_words() {
            var typesetter = new Typesetter();

            var result = typesetter.wrap("hello world", 7);

            assertThat(result)
                    .isEqualTo("""
                            hello
                            world
                            """);
        }

        @ParameterizedTest
        @ValueSource(ints = {9, 10})
        void long_line(int columns) {
            var typesetter = new Typesetter();

            var result = typesetter.wrap("a lot of words for a single line", columns);

            assertThat(result)
                    .isEqualTo("""
                            a lot of
                            words for
                            a single
                            line
                            """);
        }

        @Test
        void ignores_whitespace() {
            var typesetter = new Typesetter();

            var result = typesetter.wrap("this is a test", 4);

            assertThat(result)
                    .isEqualTo("""
                            this
                            is a
                            test
                            """);
        }

        @Test
        void splits_long_words() {
            var typesetter = new Typesetter();

            var result = typesetter.wrap("a longword", 7);

            assertThat(result)
                    .isEqualTo("""
                            a long-
                            word
                            """);
        }

        @Test
        void splits_long_words_in_multiple_lines() {
            var typesetter = new Typesetter();

            var result = typesetter.wrap("reallylongword", 7);

            assertThat(result)
                    .isEqualTo("""
                            really-
                            longwo-
                            rd
                            """);
        }

        @Test
        void works_for_lorem_ipsum() {
            var typesetter = new Typesetter();
            var text = "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus lacus, bibendum vitae tincidunt quis, vehicula non ex. Mauris vestibulum erat est, eu pulvinar sapien rhoncus non. Fusce ac elit eleifend, elementum neque non, consectetur dui. Phasellus sodales mollis finibus. Curabitur ultricies arcu eget quam tincidunt, pulvinar tristique ipsum convallis. Donec dictum interdum elit, eu gravida justo ornare vel. Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas. Donec orci magna, condimentum sed fringilla in, pellentesque ac nisl. Mauris interdum mi ac nulla pretium, ut aliquet nunc facilisis. Donec dui arcu, consectetur et purus eget, faucibus ornare nunc. Phasellus ac quam ipsum. Vestibulum sed justo aliquam, luctus eros ac, placerat lorem. Class aptent taciti sociosqu ad litora torquent per conubia nostra, per inceptos himenaeos. Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas. In nec vehicula erat, a tempor lorem. In posuere, magna vitae feugiat ultricies, arcu turpis vehicula eros, at ornare arcu arcu at purus.";

            var result = typesetter.wrap(text, 8);

            assertThat(result)
                    .isEqualTo("""
                            Lorem
                            ipsum
                            dolor
                            sit
                            amet, c-
                            onsecte-
                            tur adi-
                            piscing
                            elit.
                            Aliquam
                            lectus
                            lacus,
                            bibendum
                            vitae t-
                            incidunt
                            quis,
                            vehicula
                            non ex.
                            Mauris
                            vestibu-
                            lum erat
                            est, eu
                            pulvinar
                            sapien
                            rhoncus
                            non.
                            Fusce ac
                            elit el-
                            eifend,
                            element-
                            um neque
                            non, co-
                            nsectet-
                            ur dui.
                            Phasell-
                            us
                            sodales
                            mollis
                            finibus.
                            Curabit-
                            ur ultr-
                            icies
                            arcu
                            eget
                            quam ti-
                            ncidunt,
                            pulvinar
                            tristiq-
                            ue ipsum
                            convall-
                            is.
                            Donec
                            dictum
                            interdum
                            elit, eu
                            gravida
                            justo
                            ornare
                            vel. Pe-
                            llentes-
                            que
                            habitant
                            morbi t-
                            ristique
                            senectus
                            et netus
                            et male-
                            suada
                            fames ac
                            turpis
                            egestas.
                            Donec
                            orci
                            magna,
                            condime-
                            ntum sed
                            fringil-
                            la in,
                            pellent-
                            esque ac
                            nisl.
                            Mauris
                            interdum
                            mi ac
                            nulla
                            pretium,
                            ut
                            aliquet
                            nunc fa-
                            cilisis.
                            Donec
                            dui
                            arcu, c-
                            onsecte-
                            tur et
                            purus
                            eget,
                            faucibus
                            ornare
                            nunc. P-
                            hasellus
                            ac quam
                            ipsum.
                            Vestibu-
                            lum sed
                            justo
                            aliquam,
                            luctus
                            eros ac,
                            placerat
                            lorem.
                            Class
                            aptent
                            taciti
                            sociosqu
                            ad
                            litora
                            torquent
                            per
                            conubia
                            nostra,
                            per
                            inceptos
                            himenae-
                            os. Pel-
                            lentesq-
                            ue
                            habitant
                            morbi t-
                            ristique
                            senectus
                            et netus
                            et male-
                            suada
                            fames ac
                            turpis
                            egestas.
                            In nec
                            vehicula
                            erat, a
                            tempor
                            lorem.
                            In
                            posuere,
                            magna
                            vitae
                            feugiat
                            ultrici-
                            es, arcu
                            turpis
                            vehicula
                            eros, at
                            ornare
                            arcu
                            arcu at
                            purus.
                            """);
        }

    }
}
